# Disconnect API Usage Example

This document demonstrates how to use the new disconnect API to disconnect from a platform/serviceID.

## API Endpoint

```
DELETE /connections/:serviceId
```

## Authentication

The API requires Basic Authentication with user credentials.

## Request Examples

### Disconnect from Facebook

```bash
curl -X DELETE \
  "https://your-api-endpoint.com/connections/facebook" \
  -H "Authorization: Basic <base64-encoded-credentials>" \
  -H "Content-Type: application/json"
```

### Disconnect from Netflix

```bash
curl -X DELETE \
  "https://your-api-endpoint.com/connections/netflix" \
  -H "Authorization: Basic <base64-encoded-credentials>" \
  -H "Content-Type: application/json"
```

## Response Examples

### Successful Disconnection

```json
{
  "success": true,
  "message": "Successfully disconnected from facebook"
}
```

**Status Code:** `200 OK`

### Platform Not Connected

```json
{
  "error": "Platform not found or not connected"
}
```

**Status Code:** `404 Not Found`

### Unauthorized Request

```json
{
  "error": "Unauthorized"
}
```

**Status Code:** `401 Unauthorized`

### Server Error

```json
{
  "error": "Internal Server Error"
}
```

**Status Code:** `500 Internal Server Error`

## Supported Platforms

- `facebook`
- `netflix`
- `github`
- `google`
- `kazeel`

## JavaScript/TypeScript Example

```typescript
async function disconnectFromPlatform(serviceId: string, credentials: string) {
  try {
    const response = await fetch(`/connections/${serviceId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Basic ${credentials}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const result = await response.json();
      console.log('Disconnected successfully:', result.message);
      return true;
    } else {
      const error = await response.json();
      console.error('Failed to disconnect:', error.error);
      return false;
    }
  } catch (error) {
    console.error('Network error:', error);
    return false;
  }
}

// Usage
const credentials = btoa('username:password');
await disconnectFromPlatform('facebook', credentials);
```

## What Happens When You Disconnect

1. **Platform Status**: The platform's `connected` status is set to `false`
2. **Active Links**: All connected links for the platform are expired
3. **Agent Cleanup**: Associated Agent DOs are immediately deleted
4. **Retry Count**: The platform's retry count is reset to 0
5. **Future Connections**: You can create new connections to the platform after disconnecting

## Verification

After disconnecting, you can verify the status by calling:

```bash
curl -X GET \
  "https://your-api-endpoint.com/connections/facebook" \
  -H "Authorization: Basic <base64-encoded-credentials>"
```

The response should show `"connected": false`.
